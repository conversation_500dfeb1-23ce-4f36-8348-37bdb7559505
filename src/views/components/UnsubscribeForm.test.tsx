import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import UnsubscribeForm, { shuffle } from './UnsubscribeForm';

const mockOnComplete = vi.fn();
const mockOnDismiss = vi.fn();

const DEFAULT_PROPS: ComponentProps<typeof UnsubscribeForm> = {
  isOpen: true,
  onComplete: mockOnComplete,
  onDismiss: mockOnDismiss,
};

const renderUnsubscribeForm = (props: Partial<ComponentProps<typeof UnsubscribeForm>> = {}) => {
  return render(<UnsubscribeForm {...DEFAULT_PROPS} {...props} />);
};

describe('UnsubscribeForm', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when rendered', () => {
    describe('when isOpen is true', () => {
      it('renders the dialog', () => {
        renderUnsubscribeForm();
        expect(screen.getByText("Let us know why you're leaving")).toBeInTheDocument();
      });

      it('renders the heading', () => {
        renderUnsubscribeForm();
        expect(screen.getByText("Let us know why you're leaving")).toBeInTheDocument();
        expect(screen.getByText("Let us know why you're leaving").tagName).toBe('H2');
      });

      it('renders all predefined options', () => {
        renderUnsubscribeForm();

        // Check that some of the predefined options are rendered
        expect(screen.getByText('The subscription fee is too expensive for me.')).toBeInTheDocument();
        expect(screen.getByText("I'm not getting the value I expected.")).toBeInTheDocument();
        expect(screen.getByText('I experienced too many technical issues.')).toBeInTheDocument();
        expect(screen.getByText('I no longer have a need for the platform.')).toBeInTheDocument();
        expect(screen.getByText('I am leaving to use another platform.')).toBeInTheDocument();
      });

      it('renders the other option with input field', () => {
        renderUnsubscribeForm();
        const otherInput = screen.getByPlaceholderText('Other...');
        expect(otherInput).toBeInTheDocument();
        expect(otherInput.tagName).toBe('INPUT');
      });

      it('renders the submit button', () => {
        renderUnsubscribeForm();
        expect(screen.getByText('Cancel Subscription')).toBeInTheDocument();
      });

      it('renders checkboxes for all options', () => {
        const { container } = renderUnsubscribeForm();
        // Should have 6 checkboxes total (5 predefined + 1 other)
        const checkboxes = container.querySelectorAll('.checkbox');
        expect(checkboxes).toHaveLength(6);
      });
    });

    describe('when isOpen is false', () => {
      it('does not render the dialog', () => {
        renderUnsubscribeForm({ isOpen: false });
        expect(screen.queryByText("Let us know why you're leaving")).not.toBeInTheDocument();
      });
    });
  });

  describe('interactions', () => {
    describe('clicking predefined options', () => {
      it('selects the clicked option and deselects others', () => {
        const { container } = renderUnsubscribeForm();

        const firstOption = screen.getByText('The subscription fee is too expensive for me.');
        const secondOption = screen.getByText("I'm not getting the value I expected.");

        // Click first option
        fireEvent.click(firstOption);

        // First option should be checked
        const firstCheckbox = firstOption.parentElement?.querySelector('.checkbox');
        expect(firstCheckbox).toHaveClass('is-checked');

        // Click second option
        fireEvent.click(secondOption);

        // Second option should be checked, first should not
        const secondCheckbox = secondOption.parentElement?.querySelector('.checkbox');
        expect(secondCheckbox).toHaveClass('is-checked');
        expect(firstCheckbox).not.toHaveClass('is-checked');
      });

      it('enables the submit button when an option is selected', () => {
        renderUnsubscribeForm();

        const option = screen.getByText('The subscription fee is too expensive for me.');
        const submitButton = screen.getByText('Cancel Subscription');

        // Initially button should be disabled (no visual indication in this component)
        fireEvent.click(option);

        // After clicking an option, button should be enabled
        // We can test this by checking if clicking the button calls onComplete
        fireEvent.click(submitButton);
        expect(mockOnComplete).toHaveBeenCalledWith(
          expect.any(Object),
          'The subscription fee is too expensive for me.',
        );
      });
    });

    describe('other option interactions', () => {
      it('selects other option when clicked', () => {
        const { container } = renderUnsubscribeForm();

        const otherOption = screen.getByPlaceholderText('Other...').parentElement;
        const otherCheckbox = otherOption?.querySelector('.checkbox');

        fireEvent.click(otherOption!);

        expect(otherCheckbox).toHaveClass('is-checked');
      });

      it('enables submit button when other text is entered', () => {
        renderUnsubscribeForm();

        const otherInput = screen.getByPlaceholderText('Other...');
        const submitButton = screen.getByText('Cancel Subscription');

        fireEvent.change(otherInput, { target: { value: 'Custom reason' } });

        // Button should be enabled after entering text
        fireEvent.click(submitButton);
        expect(mockOnComplete).toHaveBeenCalledWith(expect.any(Object), 'Custom reason');
      });

      it('deselects predefined options when other is selected', () => {
        const { container } = renderUnsubscribeForm();

        // First select a predefined option
        const predefinedOption = screen.getByText('The subscription fee is too expensive for me.');
        fireEvent.click(predefinedOption);

        const predefinedCheckbox = predefinedOption.parentElement?.querySelector('.checkbox');
        expect(predefinedCheckbox).toHaveClass('is-checked');

        // Then click other option
        const otherOption = screen.getByPlaceholderText('Other...').parentElement;
        fireEvent.click(otherOption!);

        // Predefined option should be deselected
        expect(predefinedCheckbox).not.toHaveClass('is-checked');
      });
    });

    describe('submit button', () => {
      it('does not call onComplete when no option is selected', () => {
        renderUnsubscribeForm();

        const submitButton = screen.getByText('Cancel Subscription');
        fireEvent.click(submitButton);

        expect(mockOnComplete).not.toHaveBeenCalled();
      });

      it('calls onComplete with predefined reason when predefined option is selected', () => {
        renderUnsubscribeForm();

        const option = screen.getByText('I experienced too many technical issues.');
        const submitButton = screen.getByText('Cancel Subscription');

        fireEvent.click(option);
        fireEvent.click(submitButton);

        expect(mockOnComplete).toHaveBeenCalledWith(expect.any(Object), 'I experienced too many technical issues.');
      });

      it('calls onComplete with custom reason when other option is used', () => {
        renderUnsubscribeForm();

        const otherInput = screen.getByPlaceholderText('Other...');
        const submitButton = screen.getByText('Cancel Subscription');

        fireEvent.change(otherInput, { target: { value: 'My custom reason' } });
        fireEvent.click(submitButton);

        expect(mockOnComplete).toHaveBeenCalledWith(expect.any(Object), 'My custom reason');
      });
    });

    describe('dialog dismiss', () => {
      it('calls onDismiss when dialog is dismissed', () => {
        const { container } = renderUnsubscribeForm();

        const dismissButton = container.querySelector('.dialog-dismiss');
        expect(dismissButton).toBeInTheDocument();

        fireEvent.click(dismissButton!);
        expect(mockOnDismiss).toHaveBeenCalled();
      });
    });
  });
});

describe('shuffle utility function', () => {
  it('returns an array with the same length', () => {
    const original = [1, 2, 3, 4, 5];
    const shuffled = shuffle([...original]);
    expect(shuffled).toHaveLength(original.length);
  });

  it('contains all original elements', () => {
    const original = [1, 2, 3, 4, 5];
    const shuffled = shuffle([...original]);

    original.forEach((item) => {
      expect(shuffled).toContain(item);
    });
  });

  it('does not modify the original array when passed a copy', () => {
    const original = [1, 2, 3, 4, 5];
    const copy = [...original];
    shuffle(copy);

    expect(original).toEqual([1, 2, 3, 4, 5]);
  });

  it('handles empty array', () => {
    const result = shuffle([]);
    expect(result).toEqual([]);
  });

  it('handles single element array', () => {
    const result = shuffle([1]);
    expect(result).toEqual([1]);
  });
});

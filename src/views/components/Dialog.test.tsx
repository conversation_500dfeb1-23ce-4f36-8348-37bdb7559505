import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps, createRef } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Dialog, DialogSize } from './Dialog';

const mockOnDismiss = vi.fn();
const mockOnClickBackground = vi.fn();

const DEFAULT_PROPS: ComponentProps<typeof Dialog> = {
  isOpen: true,
  onDismiss: mockOnDismiss,
};

const renderDialog = (props: Partial<ComponentProps<typeof Dialog>> = {}) => {
  return render(
    <Dialog {...DEFAULT_PROPS} {...props}>
      <div>Test Content</div>
    </Dialog>,
  );
};

describe('Dialog', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when rendered', () => {
    describe('when isOpen is true', () => {
      it('renders the dialog', () => {
        renderDialog();
        expect(screen.getByText('Test Content')).toBeInTheDocument();
      });

      it('renders children content', () => {
        renderDialog();
        expect(screen.getByText('Test Content')).toBeInTheDocument();
      });

      it('renders dialog background', () => {
        const { container } = renderDialog();
        expect(container.querySelector('.dialog-background')).toBeInTheDocument();
      });

      it('renders dialog container with default classes', () => {
        const { container } = renderDialog();
        const dialogElement = container.querySelector('.dialog');
        expect(dialogElement).toBeInTheDocument();
        expect(dialogElement).toHaveClass('col-xs-8', 'col-sm-6', 'col-md-4');
      });

      it('renders dismiss button by default', () => {
        const { container } = renderDialog();
        const dismissButton = container.querySelector('.dialog-dismiss');
        expect(dismissButton).toBeInTheDocument();
        expect(dismissButton?.querySelector('svg')).toBeInTheDocument();
      });
    });

    describe('when isOpen is false', () => {
      it('does not render the dialog', () => {
        renderDialog({ isOpen: false });
        expect(screen.queryByText('Test Content')).not.toBeInTheDocument();
      });

      it('returns null', () => {
        const { container } = renderDialog({ isOpen: false });
        expect(container.firstChild).toBeNull();
      });
    });
  });

  describe('heading prop', () => {
    it('renders heading when provided', () => {
      renderDialog({ heading: 'Test Heading' });
      expect(screen.getByText('Test Heading')).toBeInTheDocument();
      expect(screen.getByText('Test Heading')).toHaveClass('dialog-heading');
    });

    it('does not render heading when not provided', () => {
      const { container } = renderDialog();
      expect(container.querySelector('.dialog-heading')).not.toBeInTheDocument();
    });

    it('does not render heading when explicitly undefined', () => {
      const { container } = renderDialog({ heading: undefined });
      expect(container.querySelector('.dialog-heading')).not.toBeInTheDocument();
    });
  });

  describe('size prop', () => {
    it('applies small size classes by default', () => {
      const { container } = renderDialog();
      const dialogElement = container.querySelector('.dialog');
      expect(dialogElement).toHaveClass('col-xs-8', 'col-sm-6', 'col-md-4');
    });

    it('applies small size classes when size is SMALL', () => {
      const { container } = renderDialog({ size: DialogSize.SMALL });
      const dialogElement = container.querySelector('.dialog');
      expect(dialogElement).toHaveClass('col-xs-8', 'col-sm-6', 'col-md-4');
    });

    it('applies medium size classes when size is MEDIUM', () => {
      const { container } = renderDialog({ size: DialogSize.MEDIUM });
      const dialogElement = container.querySelector('.dialog');
      expect(dialogElement).toHaveClass('col-xs-10', 'col-sm-8', 'col-md-6');
    });

    it('applies large size classes when size is LARGE', () => {
      const { container } = renderDialog({ size: DialogSize.LARGE });
      const dialogElement = container.querySelector('.dialog');
      expect(dialogElement).toHaveClass('col-xs-10', 'col-sm-8', 'col-md-7', 'col-lg-7', 'col-xl-7');
    });

    it('applies x-large size classes when size is X_LARGE', () => {
      const { container } = renderDialog({ size: DialogSize.X_LARGE });
      const dialogElement = container.querySelector('.dialog');
      expect(dialogElement).toHaveClass('col-xs-11', 'col-sm-11', 'col-md-11', 'col-lg-11', 'col-xl-11');
    });

    it('applies dynamic width class when size is LARGE_DYNAMIC', () => {
      const { container } = renderDialog({ size: DialogSize.LARGE_DYNAMIC });
      const dialogElement = container.querySelector('.dialog');
      expect(dialogElement).toHaveClass('dialog-width');
    });
  });

  describe('className prop', () => {
    it('applies custom className when provided', () => {
      const { container } = renderDialog({ className: 'custom-dialog-class' });
      const dialogElement = container.querySelector('.dialog');
      expect(dialogElement).toHaveClass('custom-dialog-class');
    });

    it('combines custom className with default classes', () => {
      const { container } = renderDialog({ className: 'custom-class' });
      const dialogElement = container.querySelector('.dialog');
      expect(dialogElement).toHaveClass('dialog', 'custom-class', 'col-xs-8', 'col-sm-6', 'col-md-4');
    });
  });

  describe('dismiss functionality', () => {
    describe('dismiss button visibility', () => {
      it('shows dismiss button by default', () => {
        const { container } = renderDialog();
        expect(container.querySelector('.dialog-dismiss')).toBeInTheDocument();
      });

      it('shows dismiss button when isClosable is true', () => {
        const { container } = renderDialog({ isClosable: true });
        expect(container.querySelector('.dialog-dismiss')).toBeInTheDocument();
      });

      it('shows dismiss button when isDismissible is true', () => {
        const { container } = renderDialog({ isDismissible: true });
        expect(container.querySelector('.dialog-dismiss')).toBeInTheDocument();
      });

      it('hides dismiss button when isClosable is false', () => {
        const { container } = renderDialog({ isClosable: false });
        expect(container.querySelector('.dialog-dismiss')).not.toBeInTheDocument();
      });

      it('hides dismiss button when isDismissible is false', () => {
        const { container } = renderDialog({ isDismissible: false });
        expect(container.querySelector('.dialog-dismiss')).not.toBeInTheDocument();
      });

      it('hides dismiss button when both isClosable and isDismissible are false', () => {
        const { container } = renderDialog({ isClosable: false, isDismissible: false });
        expect(container.querySelector('.dialog-dismiss')).not.toBeInTheDocument();
      });
    });

    describe('dismiss button interactions', () => {
      it('calls onDismiss when dismiss button is clicked', () => {
        const { container } = renderDialog();
        const dismissButton = container.querySelector('.dialog-dismiss');

        fireEvent.click(dismissButton!);
        expect(mockOnDismiss).toHaveBeenCalledTimes(1);
      });

      it('does not call onDismiss when isDismissible is false', () => {
        const { container } = renderDialog({ isDismissible: false });
        const dismissButton = container.querySelector('.dialog-dismiss');

        // Dismiss button should not be present
        expect(dismissButton).not.toBeInTheDocument();
        expect(mockOnDismiss).not.toHaveBeenCalled();
      });
    });
  });

  describe('background click interactions', () => {
    it('calls onDismiss when background is clicked by default', () => {
      const { container } = renderDialog();
      const background = container.querySelector('.dialog-background');

      fireEvent.click(background!);
      expect(mockOnDismiss).toHaveBeenCalledTimes(1);
    });

    it('calls onClickBackground when provided instead of onDismiss', () => {
      const { container } = renderDialog({ onClickBackground: mockOnClickBackground });
      const background = container.querySelector('.dialog-background');

      fireEvent.click(background!);
      expect(mockOnClickBackground).toHaveBeenCalledTimes(1);
      expect(mockOnDismiss).not.toHaveBeenCalled();
    });

    it('does not call onDismiss when isDismissible is false', () => {
      const { container } = renderDialog({ isDismissible: false });
      const background = container.querySelector('.dialog-background');

      fireEvent.click(background!);
      expect(mockOnDismiss).not.toHaveBeenCalled();
    });

    it('does not call onDismiss when clicking on dialog content', () => {
      const { container } = renderDialog();
      const dialogContent = container.querySelector('.dialog');

      fireEvent.click(dialogContent!);
      expect(mockOnDismiss).not.toHaveBeenCalled();
    });
  });

  describe('overflowRef prop', () => {
    it('applies ref to dialog background when provided', () => {
      const ref = createRef<HTMLDivElement>();
      renderDialog({ overflowRef: ref });

      expect(ref.current).toBeInstanceOf(HTMLDivElement);
      expect(ref.current).toHaveClass('dialog-background');
    });
  });
});
